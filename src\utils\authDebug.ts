/**
 * Authentication Debug Utilities
 * Provides debugging tools for authentication issues
 */

import { useAuthStore } from '../stores/authStore';
import { decodeJwtToken, isTokenExpired, getTokenTimeRemaining } from './jwtUtils';

export interface AuthDebugInfo {
  isAuthenticated: boolean;
  hasUser: boolean;
  hasAccessToken: boolean;
  hasRefreshToken: boolean;
  tokenValid: boolean;
  tokenExpired: boolean;
  timeRemaining: number;
  tokenPayload: any;
  userInfo: any;
  storeState: any;
}

/**
 * Get comprehensive authentication debug information
 */
export const getAuthDebugInfo = (): AuthDebugInfo => {
  const authState = useAuthStore.getState();
  const { user, accessToken, refreshToken, isAuthenticated } = authState;

  let tokenPayload = null;
  let tokenValid = false;
  let tokenExpired = true;
  let timeRemaining = 0;

  if (accessToken) {
    tokenPayload = decodeJwtToken(accessToken);
    tokenValid = !!tokenPayload;
    tokenExpired = isTokenExpired(accessToken);
    timeRemaining = getTokenTimeRemaining(accessToken);
  }

  return {
    isAuthenticated,
    hasUser: !!user,
    hasAccessToken: !!accessToken,
    hasRefreshToken: !!refreshToken,
    tokenValid,
    tokenExpired,
    timeRemaining,
    tokenPayload,
    userInfo: user,
    storeState: {
      isAuthenticated: authState.isAuthenticated,
      isLoading: authState.isLoading,
      tokenExpiresAt: authState.tokenExpiresAt,
    }
  };
};

/**
 * Log comprehensive authentication debug information
 */
export const logAuthDebugInfo = (context: string = 'Auth Debug') => {
  const debugInfo = getAuthDebugInfo();
  
  console.group(`🔒 ${context} - Authentication Debug Info`);
  console.log('📊 Overview:', {
    isAuthenticated: debugInfo.isAuthenticated,
    hasUser: debugInfo.hasUser,
    hasAccessToken: debugInfo.hasAccessToken,
    hasRefreshToken: debugInfo.hasRefreshToken,
  });
  
  console.log('🎫 Token Info:', {
    tokenValid: debugInfo.tokenValid,
    tokenExpired: debugInfo.tokenExpired,
    timeRemaining: debugInfo.timeRemaining,
    timeRemainingFormatted: formatTimeRemaining(debugInfo.timeRemaining),
  });
  
  if (debugInfo.tokenPayload) {
    console.log('📋 Token Payload:', debugInfo.tokenPayload);
  }
  
  if (debugInfo.userInfo) {
    console.log('👤 User Info:', debugInfo.userInfo);
  }
  
  console.log('🏪 Store State:', debugInfo.storeState);
  console.groupEnd();
  
  return debugInfo;
};

/**
 * Format time remaining in a human-readable format
 */
export const formatTimeRemaining = (seconds: number): string => {
  if (seconds <= 0) return 'Expired';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
};

/**
 * Check if authentication state is consistent
 */
export const validateAuthState = (): { isValid: boolean; issues: string[] } => {
  const debugInfo = getAuthDebugInfo();
  const issues: string[] = [];
  
  // Check for inconsistencies
  if (debugInfo.isAuthenticated && !debugInfo.hasAccessToken) {
    issues.push('Authenticated but no access token');
  }
  
  if (debugInfo.hasAccessToken && !debugInfo.tokenValid) {
    issues.push('Access token present but invalid');
  }
  
  if (debugInfo.isAuthenticated && debugInfo.tokenExpired) {
    issues.push('Authenticated but token is expired');
  }
  
  if (debugInfo.hasAccessToken && !debugInfo.hasUser) {
    issues.push('Access token present but no user info');
  }
  
  if (debugInfo.isAuthenticated && debugInfo.timeRemaining < 300) { // Less than 5 minutes
    issues.push(`Token expires soon (${formatTimeRemaining(debugInfo.timeRemaining)})`);
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
};

/**
 * Debug hook for components to easily access auth debug info
 */
export const useAuthDebug = () => {
  const getDebugInfo = () => getAuthDebugInfo();
  const logDebugInfo = (context?: string) => logAuthDebugInfo(context);
  const validateState = () => validateAuthState();
  
  return {
    getDebugInfo,
    logDebugInfo,
    validateState,
  };
};

/**
 * Global debug function for console access
 */
if (typeof window !== 'undefined') {
  (window as any).authDebug = {
    getInfo: getAuthDebugInfo,
    log: logAuthDebugInfo,
    validate: validateAuthState,
  };
}
