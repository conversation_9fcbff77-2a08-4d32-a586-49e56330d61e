# Google OAuth Authentication Troubleshooting Guide

## Overview
This guide helps troubleshoot the immediate session expiration issue with Google OAuth authentication.

## Changes Made

### 1. Improved Token Refresh Mechanism
- **File**: `src/apis/client.ts`
- **Changes**: Added automatic token refresh in both request and response interceptors
- **Impact**: Instead of immediately logging out users when tokens expire, the system now attempts to refresh tokens automatically

### 2. Enhanced Debug Logging
- **File**: `src/utils/jwtUtils.ts`
- **Changes**: Added comprehensive logging to token expiration checks
- **Impact**: Better visibility into when and why tokens are considered expired

### 3. Authentication Debug Tools
- **Files**: 
  - `src/utils/authDebug.ts` - Debug utilities
  - `src/components/AuthDebugPanel.tsx` - Visual debug panel
- **Impact**: Real-time monitoring of authentication state and token status

### 4. Improved Error Handling
- **Files**: 
  - `src/hooks/useGoogleLogin.ts`
  - `src/components/GoogleLoginButton.tsx`
  - `src/services/authService.ts`
- **Changes**: Added detailed logging throughout the Google login flow
- **Impact**: Better visibility into where the authentication process fails

## Testing Steps

### 1. Enable Debug Mode
1. Start your development server
2. Open the application in your browser
3. Look for the "🔒 Debug" button in the bottom-right corner
4. Click it or press `Ctrl+Shift+D` to open the debug panel

### 2. Test Google Login
1. Navigate to the login page
2. Open browser developer tools (F12)
3. Go to the Console tab
4. Click the Google Sign-In button
5. Watch the console for detailed logs starting with "🔒"

### 3. Monitor Authentication State
The debug panel shows real-time information about:
- Authentication status
- Token validity and expiration
- User information
- Any validation issues

### 4. Check for Common Issues

#### Issue 1: Token Immediately Expires
**Symptoms**: User logs in successfully but is immediately logged out
**Debug**: Look for logs showing "Token expired" immediately after login
**Possible Causes**:
- Backend returning tokens with very short expiration times
- Clock skew between client and server
- Incorrect JWT parsing

#### Issue 2: Token Refresh Fails
**Symptoms**: User stays logged in longer but eventually gets logged out
**Debug**: Look for "Token refresh failed" messages
**Possible Causes**:
- Backend refresh endpoint not working
- Refresh token invalid or expired
- Network connectivity issues

#### Issue 3: Google OAuth Configuration
**Symptoms**: Google login button doesn't work or shows configuration errors
**Debug**: Check for "Google Client ID not configured" errors
**Possible Causes**:
- Missing or incorrect `VITE_GOOGLE_CLIENT_ID` in `.env`
- Domain not authorized in Google Cloud Console
- Incorrect redirect URIs configured

## Environment Configuration

### Required Environment Variables
```bash
# .env file
VITE_API_BASE_URL=http://localhost:5057/api
VITE_GOOGLE_CLIENT_ID=your-actual-google-client-id
```

### Google Cloud Console Configuration
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to "APIs & Services" > "Credentials"
4. Find your OAuth 2.0 Client ID
5. Ensure these domains are in "Authorized JavaScript origins":
   - `http://localhost:5173` (for development)
   - Your production domain
6. Ensure these URIs are in "Authorized redirect URIs":
   - `http://localhost:5173` (for development)
   - Your production domain

## Backend Requirements

### Expected API Endpoints
Your backend must support these endpoints:

```typescript
// Google login
POST /api/Auth/google-login
Body: { credential: string }
Response: { user: User, token: string, refreshToken: string }

// Token refresh
POST /api/Auth/refresh
Body: { refreshToken: string }
Response: { token: string, refreshToken: string }

// Get Google Client ID (optional - can use env var instead)
GET /api/Config/google-client-id
Response: { clientId: string }
```

### Token Requirements
- Access tokens should have reasonable expiration times (15-60 minutes)
- Refresh tokens should have longer expiration times (days/weeks)
- JWT tokens must include proper `exp` (expiration) claims
- Token refresh should return new access tokens and optionally new refresh tokens

## Debugging Commands

### Browser Console Commands
```javascript
// Get current auth state
authDebug.getInfo()

// Log detailed auth information
authDebug.log()

// Validate auth state consistency
authDebug.validate()

// Force token expiration check
window.authDebug.log('Manual Check')
```

### Common Debug Scenarios

#### Scenario 1: Check Token Expiration
```javascript
const info = authDebug.getInfo();
console.log('Token expires in:', info.timeRemaining, 'seconds');
console.log('Token expired:', info.tokenExpired);
```

#### Scenario 2: Inspect Token Payload
```javascript
const info = authDebug.getInfo();
console.log('Token payload:', info.tokenPayload);
```

#### Scenario 3: Monitor Auth State Changes
Open the debug panel and watch the real-time updates as you perform actions.

## Fixing Common Issues

### Issue: Immediate Session Expiration
**Solution**: The token refresh mechanism should now handle this automatically. If it persists:
1. Check backend token expiration times
2. Verify refresh token endpoint works
3. Check for clock skew issues

### Issue: Google Login Button Not Working
**Solution**:
1. Verify `VITE_GOOGLE_CLIENT_ID` is set correctly
2. Check Google Cloud Console configuration
3. Ensure domains match exactly

### Issue: Network Errors
**Solution**:
1. Verify `VITE_API_BASE_URL` points to correct backend
2. Check CORS configuration on backend
3. Ensure backend is running and accessible

## Cleanup

### Remove Debug Components (After Fixing)
Once the authentication issues are resolved, remove these temporary debug components:

1. Remove `<AuthDebugPanel />` from `src/App.tsx`
2. Optionally remove debug logging from:
   - `src/utils/jwtUtils.ts`
   - `src/hooks/useGoogleLogin.ts`
   - `src/components/GoogleLoginButton.tsx`
   - `src/services/authService.ts`
3. Delete debug files:
   - `src/utils/authDebug.ts`
   - `src/components/AuthDebugPanel.tsx`

## Next Steps

1. Test the Google login flow with the debug panel open
2. Monitor console logs for detailed authentication flow information
3. If issues persist, check the specific error messages and consult this guide
4. Verify backend configuration and token handling
5. Once working, remove debug components and clean up logging

## Support

If you continue experiencing issues:
1. Capture console logs during the authentication flow
2. Note the exact error messages from the debug panel
3. Check network requests in browser dev tools
4. Verify backend logs for authentication endpoints
