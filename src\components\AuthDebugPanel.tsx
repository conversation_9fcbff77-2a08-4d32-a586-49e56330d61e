/**
 * Authentication Debug Panel
 * A temporary component to help debug authentication issues
 * Remove this component once the authentication issues are resolved
 */

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { getAuthDebugInfo, validateAuthState, formatTimeRemaining } from '../utils/authDebug';
import { isTokenExpired, getTokenTimeRemaining } from '../utils/jwtUtils';

const AuthDebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState(getAuthDebugInfo());
  const [isVisible, setIsVisible] = useState(false);
  const authStore = useAuthStore();

  // Update debug info every second
  useEffect(() => {
    const interval = setInterval(() => {
      setDebugInfo(getAuthDebugInfo());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Show/hide with keyboard shortcut
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible]);

  const validation = validateAuthState();

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-blue-600 text-white px-3 py-2 rounded-lg text-sm shadow-lg hover:bg-blue-700"
          title="Show Auth Debug Panel (Ctrl+Shift+D)"
        >
          🔒 Debug
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-xl p-4 max-w-md max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-lg">🔒 Auth Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      {/* Status Overview */}
      <div className="mb-3">
        <h4 className="font-semibold mb-2">Status</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className={`p-2 rounded ${debugInfo.isAuthenticated ? 'bg-green-100' : 'bg-red-100'}`}>
            Auth: {debugInfo.isAuthenticated ? '✅' : '❌'}
          </div>
          <div className={`p-2 rounded ${debugInfo.hasUser ? 'bg-green-100' : 'bg-red-100'}`}>
            User: {debugInfo.hasUser ? '✅' : '❌'}
          </div>
          <div className={`p-2 rounded ${debugInfo.hasAccessToken ? 'bg-green-100' : 'bg-red-100'}`}>
            Token: {debugInfo.hasAccessToken ? '✅' : '❌'}
          </div>
          <div className={`p-2 rounded ${debugInfo.hasRefreshToken ? 'bg-green-100' : 'bg-red-100'}`}>
            Refresh: {debugInfo.hasRefreshToken ? '✅' : '❌'}
          </div>
        </div>
      </div>

      {/* Token Info */}
      {debugInfo.hasAccessToken && (
        <div className="mb-3">
          <h4 className="font-semibold mb-2">Token Info</h4>
          <div className="text-sm space-y-1">
            <div className={`p-2 rounded ${debugInfo.tokenExpired ? 'bg-red-100' : 'bg-green-100'}`}>
              Status: {debugInfo.tokenExpired ? 'Expired ❌' : 'Valid ✅'}
            </div>
            <div className="p-2 bg-gray-100 rounded">
              Time Left: {formatTimeRemaining(debugInfo.timeRemaining)}
            </div>
            {debugInfo.tokenPayload && (
              <div className="p-2 bg-gray-100 rounded">
                Expires: {new Date(debugInfo.tokenPayload.exp * 1000).toLocaleString()}
              </div>
            )}
          </div>
        </div>
      )}

      {/* User Info */}
      {debugInfo.hasUser && (
        <div className="mb-3">
          <h4 className="font-semibold mb-2">User Info</h4>
          <div className="text-sm p-2 bg-gray-100 rounded">
            <div>Email: {debugInfo.userInfo?.email || 'N/A'}</div>
            <div>Name: {debugInfo.userInfo?.name || 'N/A'}</div>
            <div>Roles: {debugInfo.userInfo?.roles?.join(', ') || 'N/A'}</div>
          </div>
        </div>
      )}

      {/* Validation Issues */}
      {!validation.isValid && (
        <div className="mb-3">
          <h4 className="font-semibold mb-2 text-red-600">Issues</h4>
          <div className="text-sm space-y-1">
            {validation.issues.map((issue, index) => (
              <div key={index} className="p-2 bg-red-100 rounded text-red-700">
                ⚠️ {issue}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="space-y-2">
        <button
          onClick={() => {
            console.log('🔒 Manual auth debug log:');
            console.log(debugInfo);
          }}
          className="w-full bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700"
        >
          Log to Console
        </button>
        
        <button
          onClick={() => {
            authStore.logout();
            console.log('🔒 Manual logout triggered');
          }}
          className="w-full bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700"
        >
          Force Logout
        </button>
      </div>

      <div className="mt-3 text-xs text-gray-500">
        Press Ctrl+Shift+D to toggle this panel
      </div>
    </div>
  );
};

export default AuthDebugPanel;
