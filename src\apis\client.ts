import axios, { AxiosResponse } from 'axios';
import { useAuthStore } from '../stores/authStore';
import { isTokenExpired } from '../utils/jwtUtils';
import { showErrorToast } from '../utils/notifications';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5057/api';

// Create axios instance
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token with improved expiration handling
apiClient.interceptors.request.use(
  async (config) => {
    const authState = useAuthStore.getState();
    const { accessToken, refreshToken } = authState;

    if (accessToken) {
      // Check if token is expired before making the request
      if (isTokenExpired(accessToken)) {
        console.warn('🔒 Token expired - attempting refresh before request');

        // Try to refresh token if we have a refresh token
        if (refreshToken) {
          try {
            console.log('🔒 Attempting token refresh...');
            const response = await fetch(`${API_BASE_URL}/Auth/refresh`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ refreshToken }),
            });

            if (response.ok) {
              const data = await response.json();
              console.log('🔒 Token refresh successful');

              // Update auth state with new tokens
              authState.setTokens(data.token || data.accessToken, data.refreshToken);

              // Use the new token for this request
              config.headers.Authorization = `Bearer ${data.token || data.accessToken}`;
              return config;
            } else {
              console.warn('🔒 Token refresh failed:', response.status);
              throw new Error('Token refresh failed');
            }
          } catch (error) {
            console.error('🔒 Token refresh error:', error);
            // Fall through to logout logic
          }
        }

        // If refresh failed or no refresh token, log out user
        console.warn('🔒 No valid refresh token - logging out');
        authState.logout();
        showErrorToast(
          'Your session has expired. Please log in again.',
          'Session Expired'
        );
        window.location.href = '/login';
        return Promise.reject(new Error('Token expired and refresh failed'));
      }

      // Add valid token to request
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling expired tokens with automatic refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 Unauthorized responses (token expired/invalid)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      const authState = useAuthStore.getState();
      const { refreshToken } = authState;

      // Try to refresh token if we have a refresh token
      if (refreshToken) {
        try {
          console.log('🔒 401 error - attempting token refresh...');
          const response = await fetch(`${API_BASE_URL}/Auth/refresh`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ refreshToken }),
          });

          if (response.ok) {
            const data = await response.json();
            console.log('🔒 Token refresh successful after 401');

            // Update auth state with new tokens
            authState.setTokens(data.token || data.accessToken, data.refreshToken);

            // Retry the original request with new token
            originalRequest.headers.Authorization = `Bearer ${data.token || data.accessToken}`;
            return apiClient(originalRequest);
          } else {
            console.warn('🔒 Token refresh failed after 401:', response.status);
            throw new Error('Token refresh failed');
          }
        } catch (refreshError) {
          console.error('🔒 Token refresh error after 401:', refreshError);
          // Fall through to logout logic
        }
      }

      // If refresh failed or no refresh token, log out user
      console.warn('🔒 Authentication failed - logging out user');
      authState.logout();
      showErrorToast(
        'Your session has expired. Please log in again.',
        'Session Expired'
      );
      window.location.href = '/login';
      return Promise.reject(new Error('Authentication failed - session expired'));
    }

    // Handle other errors normally
    return Promise.reject(error);
  }
);

export default apiClient;